# 任务失败处理逻辑修复

## 问题描述

当上传或下载任务进入失败状态（如网络错误、服务器错误、用户取消等）后，系统没有自动将这些失败的任务从当前任务列表中移除，并移动到相应的历史记录中。

## 解决方案

采用简单直接的方法：在 `useGlobalProgress.ts` 中的现有任务同步逻辑中添加失败任务处理。

## 修改内容

### 修改文件：`src/composables/useGlobalProgress.ts`

#### 1. 上传任务失败处理

在 `syncTusTasksToProgress()` 方法中的任务状态处理逻辑中：

**修改前：**
```typescript
// 处理任务完成、错误或取消的情况
if (["completed", "error", "cancelled"].includes(tusTask.status) && !existingTask.endTime) {
  // ... 现有逻辑
  await addToHistory(existingTask);
  removeTask(existingTask.id);
}
```

**修改后：**
```typescript
// 🔧 修复：处理任务完成、错误或取消的情况，包括失败任务自动移动到历史记录
if (["completed", "error", "cancelled"].includes(tusTask.status) && !existingTask.endTime) {
  // ... 现有逻辑
  // 🔧 新增：失败任务自动移动到历史记录
  await addToHistory(existingTask);
  console.log(`✅ 上传任务已添加到历史记录: ${existingTask.fileName} (状态: ${tusTask.status})`);
  
  removeTask(existingTask.id);
  
  // 🔧 新增：即使添加历史记录失败，也要从当前列表中移除失败任务
  if (["error", "cancelled"].includes(tusTask.status)) {
    removeTask(existingTask.id);
    console.log(`🗑️ 强制从当前列表移除失败的上传任务: ${existingTask.fileName}`);
  }
}
```

#### 2. 下载任务失败处理

在 `syncDownloadTasksToProgress()` 方法中的任务状态处理逻辑中：

**修改前：**
```typescript
// 处理任务完成、错误或取消的情况
if (["completed", "error", "cancelled"].includes(downloadTask.status) && !existingTask.endTime) {
  // ... 现有逻辑
  await addToHistory(historyTask);
  removeTask(existingTask.id);
}
```

**修改后：**
```typescript
// 🔧 修复：处理任务完成、错误或取消的情况，包括失败任务自动移动到历史记录
if (["completed", "error", "cancelled"].includes(downloadTask.status) && !existingTask.endTime) {
  // ... 现有逻辑
  // 🔧 新增：失败任务自动移动到历史记录
  await addToHistory(historyTask);
  console.log(`✅ 下载任务已添加到历史记录: ${existingTask.fileName} (状态: ${downloadTask.status})`);
  
  removeTask(existingTask.id);
  
  // 🔧 新增：即使添加历史记录失败，也要从当前列表中移除失败任务
  if (["error", "cancelled"].includes(downloadTask.status)) {
    removeTask(existingTask.id);
    console.log(`🗑️ 强制从当前列表移除失败的下载任务: ${existingTask.fileName}`);
  }
}
```

## 实现特点

### 1. 简单直接
- 利用现有的任务同步逻辑
- 不需要修改 `useTusUpload.ts` 和 `useStreamDownloadManager.ts`
- 最小化代码变更

### 2. 自动化处理
- 失败任务无需用户手动操作，系统自动处理
- 使用现有的 `nextTick` 确保状态更新完成后再处理历史记录

### 3. 错误恢复
- 即使添加历史记录失败，也会从当前列表中移除失败任务
- 避免任务卡在当前列表中

### 4. 去重保护
- 利用现有的 `addToHistory()` 方法的去重机制
- 防止重复添加同一任务到历史记录

### 5. 状态管理一致性
- 保持与现有任务状态管理逻辑的完全一致性
- 上传和下载任务使用独立的 localStorage 存储

## 工作流程

### 上传任务失败处理流程：
1. Electron 主进程检测到上传任务失败
2. 通过 IPC 通知渲染进程更新任务状态
3. `useGlobalProgress.ts` 中的 `syncTusTasksToProgress()` 检测到失败状态
4. 自动调用 `addToHistory()` 添加到上传历史记录
5. 调用 `removeTask()` 从当前任务列表中移除

### 下载任务失败处理流程：
1. Electron 主进程检测到下载任务失败
2. 通过 IPC 通知渲染进程更新任务状态
3. `useGlobalProgress.ts` 中的 `syncDownloadTasksToProgress()` 检测到失败状态
4. 自动调用 `addToHistory()` 添加到下载历史记录
5. 调用 `removeTask()` 从当前任务列表中移除

## 失败状态类型

系统会自动处理以下失败状态：
- `error`: 任务执行过程中发生错误
- `cancelled`: 用户取消任务

## 测试建议

1. **上传任务失败测试**：
   - 断网后尝试上传文件
   - 上传过程中取消任务
   - 服务器返回错误时的处理

2. **下载任务失败测试**：
   - 断网后尝试下载文件
   - 下载过程中取消任务
   - 文件不存在时的处理

3. **历史记录验证**：
   - 验证失败任务是否正确添加到历史记录
   - 验证失败任务是否从当前列表中移除
   - 验证历史记录的去重功能

## 优势

1. **最小化修改**：只修改一个文件，利用现有逻辑
2. **高度兼容**：与现有任务管理系统完全兼容
3. **自动化**：无需用户手动操作
4. **可靠性**：即使出错也能确保任务从当前列表移除
5. **一致性**：保持与现有代码风格和逻辑的一致性

## 相关文件

- `src/composables/useGlobalProgress.ts` (主要修改)
- `src/components/Upload/composables/useTusUpload.ts` (无修改)
- `src/composables/useStreamDownloadManager.ts` (无修改)
