# 任务失败处理逻辑修复

## 概述

本次修复实现了任务失败处理逻辑，当上传或下载任务进入失败状态后，系统会自动将这些失败的任务从当前任务列表中移除，并使用现有的 `addToHistory()` 方法将它们移动到相应的历史记录中。

## 修复内容

### 1. 失败状态识别

定义了失败状态类型：
- `error`: 任务执行过程中发生错误
- `cancelled`: 用户取消任务
- `timeout`: 任务执行超时

### 2. 上传任务失败处理 (useTusUpload.ts)

#### 新增功能：
- `isFailureStatus()`: 检查状态是否为失败状态
- `convertUploadTaskToProgressTask()`: 将上传任务转换为进度任务格式
- `handleFailedTaskAutoMove()`: 处理失败任务的自动移动到历史记录

#### 修改的方法：
- `handleTaskStatusChanged()`: 添加失败状态检测和自动处理
- `handleTaskError()`: 添加错误任务自动移动逻辑

### 3. 下载任务失败处理 (useStreamDownloadManager.ts)

#### 新增功能：
- `isFailureStatus()`: 检查状态是否为失败状态
- `convertDownloadTaskToProgressTask()`: 将下载任务转换为进度任务格式
- `handleFailedTaskAutoMove()`: 处理失败任务的自动移动到历史记录

#### 修改的方法：
- `handleTaskStatusChanged()`: 添加失败状态检测和自动处理
- `handleTaskError()`: 添加错误任务自动移动逻辑

### 4. 全局进度管理 (useGlobalProgress.ts)

#### 修改内容：
- 在返回对象中导出 `addToHistory` 方法，使其可以被其他 composables 使用

## 实现特点

### 1. 自动化处理
- 失败任务无需用户手动操作，系统自动处理
- 使用 `nextTick` 确保状态更新完成后再处理历史记录

### 2. 错误处理
- 即使添加历史记录失败，也会从当前列表中移除任务，避免任务卡在列表中
- 完整的错误日志记录，便于调试

### 3. 去重保护
- 利用现有的 `addToHistory()` 方法的去重机制
- 防止重复添加同一任务到历史记录

### 4. 状态管理一致性
- 保持与现有任务状态管理逻辑的一致性
- 上传和下载任务使用独立的 localStorage 存储

### 5. 类型安全
- 正确的状态映射和类型转换
- 完整的 TypeScript 类型支持

## 工作流程

### 上传任务失败处理流程：
1. Electron 主进程检测到上传任务失败
2. 通过 IPC 通知渲染进程 (`handleTaskStatusChanged` 或 `handleTaskError`)
3. 检测到失败状态，触发 `handleFailedTaskAutoMove`
4. 将 `UploadTask` 转换为 `ProgressTask` 格式
5. 调用 `addToHistory` 添加到上传历史记录
6. 从当前任务列表中移除失败任务

### 下载任务失败处理流程：
1. Electron 主进程检测到下载任务失败
2. 通过 IPC 通知渲染进程 (`handleTaskStatusChanged` 或 `handleTaskError`)
3. 检测到失败状态，触发 `handleFailedTaskAutoMove`
4. 将 `DownloadTask` 转换为 `ProgressTask` 格式
5. 调用 `addToHistory` 添加到下载历史记录
6. 从当前任务列表中移除失败任务

## 测试建议

### 1. 上传任务失败测试
- 测试网络错误导致的上传失败
- 测试用户取消上传
- 测试服务器错误导致的上传失败

### 2. 下载任务失败测试
- 测试网络错误导致的下载失败
- 测试用户取消下载
- 测试文件不存在导致的下载失败

### 3. 历史记录验证
- 验证失败任务是否正确添加到历史记录
- 验证失败任务是否从当前列表中移除
- 验证历史记录的去重功能

## 注意事项

1. **异步处理**: 使用 `nextTick` 确保状态更新完成后再处理历史记录
2. **错误恢复**: 即使历史记录添加失败，也要确保任务从当前列表移除
3. **性能考虑**: 大量任务同时失败时不会阻塞 UI
4. **兼容性**: 与现有的任务管理逻辑完全兼容

## 相关文件

- `src/components/Upload/composables/useTusUpload.ts`
- `src/composables/useStreamDownloadManager.ts`
- `src/composables/useGlobalProgress.ts`
